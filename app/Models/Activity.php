<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'route_points',
        'activity_date',
        'activity_time',
        'duration',
        'pace',
        'activity_type',
        'gpx_data',
        'avg_heart_rate',
        'cadence',
        'distance'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'route_points' => 'json',
        'activity_date' => 'date',
        'duration' => 'float',
        'pace' => 'float',
    ];
    
    /**
     * Get the user that owns the activity.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the exports for this activity.
     */
    public function exports()
    {
        return $this->hasMany(ExportedActivity::class);
    }
}
