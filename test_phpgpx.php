<?php
// A simple test to verify the phpGPX integration works correctly

require __DIR__ . '/vendor/autoload.php';

use phpGPX\Models\Extensions;
use phpGPX\Models\Extensions\TrackPointExtension;
use phpGPX\Models\GpxFile;
use phpGPX\Models\Metadata;
use phpGPX\Models\Point;
use phpGPX\Models\Segment;
use phpGPX\Models\Track;
use phpGPX\phpGPX;

// Configure phpGPX
phpGPX::$PRETTY_PRINT = true;
phpGPX::$DATETIME_FORMAT = 'Y-m-d\TH:i:s\Z';
phpGPX::$DATETIME_TIMEZONE_OUTPUT = 'UTC';

// Create a GPX file
$gpxFile = new GpxFile();

// Set metadata
$gpxFile->metadata = new Metadata();
$gpxFile->metadata->time = new \DateTime();
$gpxFile->metadata->description = "Test GPX file";

// Create a track
$track = new Track();
$track->name = "Test Run";
$track->type = "RUN";

// Create a segment
$segment = new Segment();

// Create sample points
$points = [
    [
        'lat' => 37.7749,
        'lng' => -122.4194,
        'time' => date('Y-m-d\TH:i:s\Z', strtotime('now')),
        'elevation' => 1.5,
        'heartRate' => 140
    ],
    [
        'lat' => 37.7750,
        'lng' => -122.4195,
        'time' => date('Y-m-d\TH:i:s\Z', strtotime('+1 minute')),
        'elevation' => 1.7,
        'heartRate' => 145
    ]
];

// Add points to segment
foreach ($points as $pointData) {
    $point = new Point(Point::TRACKPOINT);
    $point->latitude = $pointData['lat'];
    $point->longitude = $pointData['lng'];
    $point->elevation = $pointData['elevation'];
    $point->time = new \DateTime($pointData['time']);
    
    // Properly create extensions
    if (isset($pointData['heartRate'])) {
        // Create a TrackPointExtension
        $trackPointExt = new TrackPointExtension();
        $trackPointExt->hr = $pointData['heartRate'];
        
        // Create a new Extensions container and add the extension
        $extensions = new Extensions();
        $extensions->trackPointExtension = $trackPointExt;
        
        // Set the extensions on the point
        $point->extensions = $extensions;
    }
    
    $segment->points[] = $point;
}

// Add segment to track
$track->segments[] = $segment;

// Calculate stats
$track->recalculateStats();

// Add track to file
$gpxFile->tracks[] = $track;

// Generate XML
$xml = $gpxFile->toXML()->saveXML();

// Output XML for verification
echo $xml;

// Success message
echo "\n\nGPX generation test completed successfully!\n";
