<template>
    <div>
        <h1 class="text-3xl font-bold text-blue-600 mb-6">Create Route & Activity</h1>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Map and Route Details Section -->
            <div class="md:col-span-2">
                <div class="bg-white p-4 rounded-lg shadow-md mb-4">
                    <div id="map" class="map-container rounded"></div>
                </div>

                <div class="bg-white p-4 rounded-lg shadow-md mb-4">
                    <h2 class="text-xl font-bold text-blue-600 mb-2">Route Details</h2>
                    <div class="flex flex-wrap -mx-2">
                        <div class="w-1/2 px-2 mb-4">
                            <div class="border rounded p-3">
                                <p class="text-sm text-gray-600">Distance</p>
                                <p class="text-xl font-bold">{{ distance }} km</p>
                            </div>
                        </div>
                        <div class="w-1/2 px-2 mb-4">
                            <div class="border rounded p-3">
                                <p class="text-sm text-gray-600">Elevation Gain</p>
                                <p class="text-xl font-bold">{{ elevationGain }} m</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Preview Section -->
                <div v-if="activityGenerated" class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-blue-600 mb-4">Activity Preview</h2>
                    <div class="space-y-4">
                        <div class="border rounded p-4">
                            <h3 class="font-semibold mb-2">Activity Summary</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                    <p class="text-sm text-gray-600">Distance</p>
                                    <p class="font-bold">{{ distance }} km</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Duration</p>
                                    <p class="font-bold">{{ formatDuration(calculatedDuration) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Avg Pace</p>
                                    <p class="font-bold">{{ pace }} min/km</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Avg Heart Rate</p>
                                    <p class="font-bold">{{ avgHeartRate }} bpm</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button @click="goToExport" class="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">Continue to Export</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Route and Activity Options Section -->
            <div class="space-y-4">
                <!-- Route Options Panel -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-blue-600 mb-4">Route Options</h2>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="route-name">Route Name</label>
                        <input type="text" id="route-name" v-model="routeName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="route-type">Route Type</label>
                        <select id="route-type" v-model="routeType" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="run">Run</option>
                            <option value="bike">Bike</option>
                            <option value="hike">Hike</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Drawing Options</label>
                        <div class="flex space-x-2">
                            <button @click="setDrawingMode('point')" class="flex-1 bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded" :class="{ 'bg-blue-500 text-white hover:bg-blue-600': drawingMode === 'point' }">Add Points</button>
                            <button @click="setDrawingMode('line')" class="flex-1 bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded" :class="{ 'bg-blue-500 text-white hover:bg-blue-600': drawingMode === 'line' }">Draw Line</button>
                        </div>
                    </div>

                    <div class="mb-4">
                        <button @click="clearRoute" class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600">Clear Route</button>
                    </div>
                </div>

                <!-- Activity Parameters Panel -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-blue-600 mb-4">Activity Parameters</h2>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="activity-name">Activity Name</label>
                        <input type="text" id="activity-name" v-model="activityName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="activity-date">Activity Date</label>
                        <input type="date" id="activity-date" v-model="activityDate" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="activity-time">Start Time</label>
                        <input type="time" id="activity-time" v-model="activityTime" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="pace">Pace (min/km)</label>
                        <input type="number" id="pace" v-model="pace" step="0.1" min="3" max="15" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="range" v-model="pace" min="3" max="15" step="0.1" class="w-full mt-2">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="pace-variability">Pace Variability (%)</label>
                        <input type="number" id="pace-variability" v-model="paceVariability" min="0" max="30" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="range" v-model="paceVariability" min="0" max="30" class="w-full mt-2">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="avg-heart-rate">Avg Heart Rate (bpm)</label>
                        <input type="number" id="avg-heart-rate" v-model="avgHeartRate" min="100" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="range" v-model="avgHeartRate" min="100" max="200" class="w-full mt-2">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="cadence">Cadence (steps/min)</label>
                        <input type="number" id="cadence" v-model="cadence" min="150" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="range" v-model="cadence" min="150" max="200" class="w-full mt-2">
                    </div>
                </div>

                <!-- Generate Activity Button -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <button
                        @click="generateActivity"
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                        :disabled="routePoints.length < 2">
                        Generate Activity
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

export default {
    name: 'CombinedRouteActivity',
    setup() {
        const router = useRouter();
        const map = ref(null);
        const routeLayer = ref(null);
        const routePoints = ref([]);
        const distance = ref(0);
        const elevationGain = ref(0);
        const routeName = ref('');
        const routeType = ref('run');
        const drawingMode = ref('point');

        // Activity parameters
        const activityName = ref('');
        const activityDate = ref(new Date().toISOString().split('T')[0]);
        const activityTime = ref('08:00');
        const pace = ref(5.5);
        const paceVariability = ref(5);
        const avgHeartRate = ref(155);
        const cadence = ref(170);
        const activityGenerated = ref(false);

        // Computed properties
        const calculatedDuration = computed(() => {
            // Convert pace (min/km) to seconds and multiply by distance
            return pace.value * 60 * parseFloat(distance.value || 0);
        });

        onMounted(() => {
            initMap();
        });

        const initMap = () => {
            // Initialize the map
            map.value = L.map('map').setView([40.7128, -74.0060], 13);

            // Add the OpenStreetMap tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map.value);

            // Initialize the route layer
            routeLayer.value = L.featureGroup().addTo(map.value);

            // Add click event handler for the map
            map.value.on('click', handleMapClick);
        };

        const handleMapClick = (e) => {
            if (drawingMode.value === 'point') {
                addRoutePoint(e.latlng);
            }
        };

        const addRoutePoint = (latlng) => {
            // Add a marker at the clicked location
            L.marker(latlng).addTo(routeLayer.value);

            // Add the point to our route points array
            routePoints.value.push([latlng.lat, latlng.lng]);

            // If we have at least 2 points, draw a line
            if (routePoints.value.length >= 2) {
                drawRouteLine();
            }

            // Calculate route metrics
            calculateRouteMetrics();
        };

        const drawRouteLine = () => {
            // Clear existing polyline
            routeLayer.value.eachLayer(layer => {
                if (layer instanceof L.Polyline) {
                    routeLayer.value.removeLayer(layer);
                }
            });

            // Draw a new polyline with all points
            L.polyline(routePoints.value, { color: 'blue', weight: 4 }).addTo(routeLayer.value);
        };

        const calculateRouteMetrics = () => {
            // Calculate distance (a simplified version)
            let totalDistance = 0;
            for (let i = 1; i < routePoints.value.length; i++) {
                const p1 = L.latLng(routePoints.value[i-1][0], routePoints.value[i-1][1]);
                const p2 = L.latLng(routePoints.value[i][0], routePoints.value[i][1]);
                totalDistance += p1.distanceTo(p2);
            }

            // Convert to kilometers and round to 2 decimal places
            distance.value = (totalDistance / 1000).toFixed(2);

            // For demo purposes, generate a random elevation gain
            elevationGain.value = Math.floor(Math.random() * 100) + 50;
        };

        const setDrawingMode = (mode) => {
            drawingMode.value = mode;
        };

        const clearRoute = () => {
            routeLayer.value.clearLayers();
            routePoints.value = [];
            distance.value = 0;
            elevationGain.value = 0;
            activityGenerated.value = false;
        };

        const generateActivity = () => {
            if (routePoints.value.length < 2) {
                alert('Please create a route first by adding at least 2 points on the map');
                return;
            }

            // Save the route data
            const routeData = {
                name: routeName.value || 'Unnamed Route',
                type: routeType.value,
                points: routePoints.value,
                distance: distance.value,
                elevationGain: elevationGain.value,
                createdAt: new Date().toISOString()
            };

            let savedRoutes = JSON.parse(localStorage.getItem('createyourrun_routes') || '[]');
            savedRoutes.push(routeData);
            localStorage.setItem('createyourrun_routes', JSON.stringify(savedRoutes));

            // Mark as generated
            activityGenerated.value = true;

            // Save the activity parameters for export
            const activityData = {
                route: routeData,
                name: activityName.value || `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`,
                date: activityDate.value,
                time: activityTime.value,
                pace: pace.value,
                paceVariability: paceVariability.value,
                avgHeartRate: avgHeartRate.value,
                cadence: cadence.value,
                duration: calculatedDuration.value
            };

            localStorage.setItem('createyourrun_current_activity', JSON.stringify(activityData));
        };

        const formatDuration = (seconds) => {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);

            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        };

        const goToExport = () => {
            router.push({ name: 'export-activity' });
        };

        // When activityDate or routeType changes, update the activityName if it's empty
        watch([activityDate, routeType], () => {
            if (!activityName.value) {
                activityName.value = `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`;
            }
        });

        return {
            // Route properties
            distance,
            elevationGain,
            routeName,
            routeType,
            drawingMode,
            routePoints,
            setDrawingMode,
            clearRoute,

            // Activity properties
            activityName,
            activityDate,
            activityTime,
            pace,
            paceVariability,
            avgHeartRate,
            cadence,
            activityGenerated,
            calculatedDuration,

            // Methods
            generateActivity,
            formatDuration,
            goToExport
        };
    }
}
</script>

<style scoped>
.map-container {
    height: 400px;
    width: 100%;
}
</style>
