<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Generate Activity</h1>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="md:col-span-2">
                <div class="bg-white p-4 rounded-lg shadow-md mb-6">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">Route Creator</h2>

                    <!-- Add search box above the map -->
                    <div class="mb-2">
                        <div class="flex">
                            <input
                                type="text"
                                v-model="searchQuery"
                                @keyup.enter="searchLocation"
                                placeholder="Search for a location..."
                                class="w-full border rounded-l px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange"
                            >
                            <button
                                @click="searchLocation"
                                class="bg-strava-orange text-white px-4 py-2 rounded-r hover:bg-strava-orangeLight"
                            >
                                Search
                            </button>
                        </div>
                        <p v-if="searchError" class="text-red-500 text-sm mt-1">{{ searchError }}</p>
                    </div>
                    <div id="map" class="map-container rounded mb-4"></div>

                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <p class="text-strava-grayLight">{{ distance }} km | {{ elevationGain }} m elevation gain</p>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="setDrawingMode('point')" class="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm" :class="{ 'bg-strava-orange text-white hover:bg-strava-orangeLight': drawingMode === 'point' }">Add Points</button>
                            <button @click="setDrawingMode('line')" class="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm" :class="{ 'bg-strava-orange text-white hover:bg-strava-orangeLight': drawingMode === 'line' }">Draw Line</button>
                            <button @click="clearRoute" class="bg-strava-grayMedium text-white px-3 py-1 rounded text-sm hover:bg-strava-gray">Clear</button>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">Activity Preview</h2>

                    <div v-if="routePoints.length > 0 && activityGenerated" class="space-y-4">
                        <div class="border rounded p-4">
                            <h3 class="font-semibold mb-2 text-strava-grayMedium">Activity Summary</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                    <p class="text-sm text-strava-grayLight">Distance</p>
                                    <p class="font-bold text-strava-gray">{{ distance }} km</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Duration</p>
                                    <p class="font-bold text-strava-gray">{{ formatDuration(calculatedDuration) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Avg Pace</p>
                                    <p class="font-bold text-strava-gray">{{ pace }} min/km</p>
                                </div>
                                <div>
                                    <p class="text-sm text-strava-grayLight">Avg Heart Rate</p>
                                    <p class="font-bold text-strava-gray">{{ avgHeartRate }} bpm</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button @click="goToExport" class="bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">Continue to Export</button>
                        </div>
                    </div>

                    <div v-else class="text-center py-8">
                        <p class="text-strava-grayLight mb-4">Configure your activity parameters and click Generate</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-md h-fit">
                <h2 class="text-xl font-bold text-strava-gray mb-4">Activity Parameters</h2>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-name">Activity Name</label>
                    <input type="text" id="activity-name" v-model="activityName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="route-type">Activity Type</label>
                    <select id="route-type" v-model="routeType" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                        <option value="running">Run</option>
                        <option value="bike">Bike</option>
                        <option value="hike">Hike</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-date">Activity Date</label>
                    <input type="date" id="activity-date" v-model="activityDate" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="activity-time">Start Time</label>
                    <input type="time" id="activity-time" v-model="activityTime" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="pace">Pace (min/km)</label>
                    <input type="number" id="pace" v-model="pace" step="0.1" min="3" max="15" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                    <input type="range" v-model="pace" min="3" max="15" step="0.1" class="w-full mt-2 accent-strava-orange">
                    <p class="text-sm text-strava-grayLight mt-1">
                        {{ formatPaceDisplay(pace) }} (This is how it will appear in Strava)
                    </p>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="pace-variability">Pace Variability (%)</label>
                    <input type="number" id="pace-variability" v-model="paceVariability" step="1" min="0" max="100" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="avg-heart-rate">Avg Heart Rate (bpm)</label>
                    <input type="number" id="avg-heart-rate" v-model="avgHeartRate" step="1" min="60" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="cadence">Cadence (spm)</label>
                    <input type="number" id="cadence" v-model="cadence" step="1" min="150" max="200" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="flex justify-end">
                    <button @click="generateActivity" class="bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">Generate</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

export default {
    name: 'ActivityGenerator',
    setup() {
        const router = useRouter();
        const map = ref(null);
        const routeLayer = ref(null);
        const routePoints = ref([]);
        const distance = ref(0);
        const elevationGain = ref(0);
        const drawingMode = ref('point');
        const searchQuery = ref('');
        const searchError = ref('');

        const activityName = ref('');
        const activityDate = ref(new Date().toISOString().split('T')[0]);
        const activityTime = ref('08:00');
        const routeType = ref('running');
        const pace = ref(5.5);
        const paceVariability = ref(5);
        const avgHeartRate = ref(155);
        const cadence = ref(170);
        const activityGenerated = ref(false);

        // Computed properties
        const calculatedDuration = computed(() => {
            if (routePoints.value.length < 2) return 0;
            // Convert pace (min/km) to seconds and multiply by distance
            return pace.value * 60 * parseFloat(distance.value);
        });

        onMounted(() => {
            // Initialize the map
            initMap();
        });

        const initMap = () => {
            // Fix Leaflet's default icon path issue
            delete L.Icon.Default.prototype._getIconUrl;
            L.Icon.Default.mergeOptions({
                iconRetinaUrl: '/images/leaflet/marker-icon-2x.png',
                iconUrl: '/images/leaflet/marker-icon.png',
                shadowUrl: '/images/leaflet/marker-shadow.png'
            });

            // Initialize the map
            map.value = L.map('map').setView([16.0544, 108.2022], 13);

            // Add the OpenStreetMap tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map.value);

            // Initialize the route layer
            routeLayer.value = L.featureGroup().addTo(map.value);

            // Add click event handler for the map
            map.value.on('click', handleMapClick);
        };

        const handleMapClick = (e) => {
            if (drawingMode.value === 'point') {
                addRoutePoint(e.latlng);
            }
        };

        const addRoutePoint = (latlng) => {
            // Add a marker at the clicked location
            L.marker(latlng).addTo(routeLayer.value);

            // Add the point to our route points array
            routePoints.value.push([latlng.lat, latlng.lng]);

            // If we have at least 2 points, draw a line
            if (routePoints.value.length >= 2) {
                drawRouteLine();
            }

            // Calculate route metrics
            calculateRouteMetrics();
        };

        const drawRouteLine = () => {
            // Clear existing polyline
            routeLayer.value.eachLayer(layer => {
                if (layer instanceof L.Polyline) {
                    routeLayer.value.removeLayer(layer);
                }
            });

            // Draw a new polyline with all points - using Strava orange color
            L.polyline(routePoints.value, { color: '#FC4C02', weight: 4 }).addTo(routeLayer.value);
        };

        // Rest of the code remains the same
        const calculateRouteMetrics = () => {
            // Calculate distance (a simplified version)
            let totalDistance = 0;
            for (let i = 1; i < routePoints.value.length; i++) {
                const p1 = L.latLng(routePoints.value[i-1][0], routePoints.value[i-1][1]);
                const p2 = L.latLng(routePoints.value[i][0], routePoints.value[i][1]);
                totalDistance += p1.distanceTo(p2);
            }

            // Convert to kilometers and round to 2 decimal places
            distance.value = (totalDistance / 1000).toFixed(2);

            // For demo purposes, generate a random elevation gain
            elevationGain.value = Math.floor(Math.random() * 100) + 50;
        };

        const setDrawingMode = (mode) => {
            drawingMode.value = mode;
        };

        const clearRoute = () => {
            routeLayer.value.clearLayers();
            routePoints.value = [];
            distance.value = 0;
            elevationGain.value = 0;
            activityGenerated.value = false;
        };

        const searchLocation = async () => {
            if (!searchQuery.value) {
                searchError.value = 'Please enter a location to search.';
                return;
            }

            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${searchQuery.value}`);
                const data = await response.json();

                if (data.length === 0) {
                    searchError.value = 'No results found. Please try a different location.';
                    return;
                }

                const { lat, lon } = data[0];
                map.value.setView([lat, lon], 13);
                searchError.value = '';
            } catch (error) {
                searchError.value = 'An error occurred while searching. Please try again.';
            }
        };

        const generateActivity = () => {
            if (routePoints.value.length < 2) {
                alert('Please create a route first by adding points on the map');
                return;
            }

            // Mark the activity as generated
            activityGenerated.value = true;

            // Create a route object
            const routeData = {
                name: activityName.value || `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`,
                type: routeType.value,
                points: routePoints.value,
                distance: distance.value,
                elevationGain: elevationGain.value
            };

            // Save the activity parameters for export
            const activityData = {
                route: routeData,
                name: activityName.value || `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${activityDate.value}`,
                date: activityDate.value,
                time: activityTime.value,
                pace: pace.value,
                paceVariability: paceVariability.value,
                avgHeartRate: avgHeartRate.value,
                cadence: cadence.value,
                duration: calculatedDuration.value
            };

            localStorage.setItem('createyourrun_current_activity', JSON.stringify(activityData));
        };

        const formatDuration = (seconds) => {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);

            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        };

        const formatPaceDisplay = (paceValue) => {
            const minutes = Math.floor(paceValue);
            const seconds = Math.floor((paceValue - minutes) * 60);
            return `${minutes}:${seconds.toString().padStart(2, '0')} min/km`;
        };

        const goToExport = () => {
            router.push({ name: 'export-activity' });
        };

        // When activityDate changes, update the activityName if it's empty
        watch(activityDate, (newDate) => {
            if (!activityName.value) {
                activityName.value = `${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} on ${newDate}`;
            }
        });

        return {
            routePoints,
            distance,
            elevationGain,
            drawingMode,
            setDrawingMode,
            clearRoute,
            searchQuery,
            searchError,
            searchLocation,
            activityName,
            activityDate,
            activityTime,
            routeType,
            pace,
            paceVariability,
            avgHeartRate,
            cadence,
            activityGenerated,
            calculatedDuration,
            generateActivity,
            formatDuration,
            formatPaceDisplay,
            goToExport
        };
    }
}
</script>

<style>
.map-container {
    height: 400px;
    width: 100%;
}

/* Strava style customizations */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: #E6E6EB;
    border-radius: 2px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FC4C02;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #FC4C02;
    cursor: pointer;
}
</style>

