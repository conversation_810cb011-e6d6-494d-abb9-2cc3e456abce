<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Export Activity</h1>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="md:col-span-2">
                <div class="bg-white p-4 rounded-lg shadow-md mb-6">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">Activity Summary</h2>

                    <div v-if="activityData">
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-strava-gray">{{ activityData.name }}</h3>
                            <p class="text-strava-grayLight">{{ formatDate(activityData.date) }} at {{ activityData.time }}</p>
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div>
                                <p class="text-sm text-strava-grayLight">Distance</p>
                                <p class="font-bold text-strava-gray">{{ activityData.route.distance }} km</p>
                            </div>
                            <div>
                                <p class="text-sm text-strava-grayLight">Duration</p>
                                <p class="font-bold text-strava-gray">{{ formatDuration(activityData.duration) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-strava-grayLight">Avg Pace</p>
                                <p class="font-bold text-strava-gray">{{ activityData.pace }} min/km</p>
                            </div>
                            <div>
                                <p class="text-sm text-strava-grayLight">Avg Heart Rate</p>
                                <p class="font-bold text-strava-gray">{{ activityData.avgHeartRate }} bpm</p>
                            </div>
                        </div>

                        <!-- File preview section hidden as requested -->
                        <div v-if="false && user && hasTokens" class="bg-strava-surface p-4 rounded-lg">
                            <h3 class="font-semibold mb-2 text-strava-grayMedium">File Preview</h3>
                            <pre class="bg-white p-3 rounded text-xs overflow-auto max-h-60 text-strava-grayMedium border border-gray-200">{{ filePreview }}</pre>
                        </div>

                        <div v-if="false && activityData && (!user || !hasTokens)" class="bg-strava-surface p-4 rounded-lg text-center">
                            <p class="text-strava-grayMedium mb-2">File preview available after login with tokens</p>
                            <div class="mt-2">
                                <button v-if="!user" @click="router.push({ name: 'login' })" class="text-strava-orange underline mr-2">Login</button>
                                <button v-if="user && !hasTokens" @click="router.push({ name: 'purchase-tokens' })" class="text-strava-orange underline">Purchase Tokens</button>
                            </div>
                        </div>
                    </div>

                    <div v-else class="border border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <p class="text-strava-grayLight mb-4">No activity data available</p>
                        <button @click="goToActivityGenerator" class="bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">Generate an Activity</button>
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-strava-gray mb-4">What Next?</h2>

                    <div class="space-y-4">
                        <div class="border-l-4 border-strava-orange pl-4">
                            <h3 class="font-semibold text-strava-gray">Upload to Strava</h3>
                            <p class="text-strava-grayLight mb-2">Export your activity and upload it to Strava</p>
                            <a href="https://www.strava.com/upload/select" target="_blank" class="text-strava-orange hover:underline">Go to Strava Upload</a>
                        </div>

                        <div class="border-l-4 border-strava-orangeLight pl-4">
                            <h3 class="font-semibold text-strava-gray">Create Another Activity</h3>
                            <p class="text-strava-grayLight mb-2">Generate a new activity using the same or a different route</p>
                            <button @click="goToActivityGenerator" class="text-strava-orange hover:underline">Create Another Activity</button>
                        </div>

                        <div class="border-l-4 border-strava-grayMedium pl-4">
                            <h3 class="font-semibold text-strava-gray">Design a New Route</h3>
                            <p class="text-strava-grayLight mb-2">Go back to the route creator to design a new running route</p>
                            <button @click="goToRouteCreator" class="text-strava-orange hover:underline">Design New Route</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-md h-fit">
                <h2 class="text-xl font-bold text-strava-gray mb-4">Export Options</h2>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="file-format">File Format</label>
                    <select id="file-format" v-model="fileFormat" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                        <option value="gpx">GPX</option>
                        <option value="tcx">TCX</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2">Data to Include</label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="include-hr" v-model="includeHeartRate" class="mr-2 accent-strava-orange">
                            <label for="include-hr" class="text-strava-grayMedium">Heart Rate</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="include-cadence" v-model="includeCadence" class="mr-2 accent-strava-orange">
                            <label for="include-cadence" class="text-strava-grayMedium">Cadence</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="include-elevation" v-model="includeElevation" class="mr-2 accent-strava-orange">
                            <label for="include-elevation" class="text-strava-grayMedium">Elevation</label>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-strava-grayLight mb-2" for="file-name">File Name</label>
                    <input type="text" id="file-name" v-model="fileName" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange">
                </div>

                <div class="mt-6">
                    <div v-if="authError" class="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
                        <p>{{ authError }}</p>
                        <div class="mt-2">
                            <button v-if="!user" @click="router.push({ name: 'login' })" class="text-red-700 underline mr-2">Login</button>
                            <button v-if="user && !hasTokens" @click="router.push({ name: 'purchase-tokens' })" class="text-red-700 underline">Purchase Tokens</button>
                        </div>
                    </div>

                    <div v-if="user && hasTokens" class="mb-4 p-3 bg-green-100 border-l-4 border-green-500 text-green-700">
                        <p>You have {{ user.tokens }} token{{ user.tokens !== 1 ? 's' : '' }} remaining</p>
                    </div>

                    <button @click="downloadFile" class="w-full bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight"
                            :disabled="!activityData || !user || !hasTokens">Download File</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getUser } from '../utils/auth';
import axios from 'axios';

export default {
    name: 'ExportActivity',
    setup() {
        const router = useRouter();
        const activityData = ref(null);
        const fileFormat = ref('gpx');
        const includeHeartRate = ref(true);
        const includeCadence = ref(true);
        const includeElevation = ref(true);
        const fileName = ref('');
        const user = ref(null);
        const hasTokens = ref(false);
        const authError = ref('');
        const activityId = ref(null); // Add this to store the database activity ID

        onMounted(() => {
            loadActivityData();
            checkUserAuth();
        });

        const checkUserAuth = async () => {
            // Get current user info from localStorage
            const currentUser = getUser();

            if (!currentUser) {
                authError.value = 'You must be logged in to export activities';
                return;
            }

            try {
                // Get fresh user data from API to check token count
                const response = await axios.get('/api/user');
                user.value = response.data;
                hasTokens.value = user.value.tokens > 0;

                if (!hasTokens.value) {
                    authError.value = 'You need tokens to export activities. Please purchase more tokens.';
                }
            } catch (error) {
                console.error('Authentication error:', error);
                authError.value = 'Authentication error. Please login again.';
            }
        };

        const loadActivityData = async () => {
            // Get the activity ID from localStorage first
            const savedActivity = localStorage.getItem('createyourrun_current_activity');
            if (savedActivity) {
                const localActivityData = JSON.parse(savedActivity);

                try {
                    // Fetch the activity from the API if user is logged in
                    const currentUser = getUser();
                    if (currentUser) {
                        // Try to find the activity in the database based on name and date
                        const activitiesResponse = await axios.get('/api/activities');
                        const activities = activitiesResponse.data;

                        // Look for matching activity by name and date
                        const matchingActivity = activities.find(activity =>
                            activity.name === localActivityData.name &&
                            activity.activity_date.substring(0, 10) === localActivityData.date);

                        if (matchingActivity) {
                            // Store the database ID for later use
                            activityId.value = matchingActivity.id;

                            // Format activity data from database to match the expected structure
                            activityData.value = {
                                id: matchingActivity.id,
                                name: matchingActivity.name,
                                date: matchingActivity.activity_date,
                                time: matchingActivity.activity_time,
                                duration: matchingActivity.duration,
                                pace: matchingActivity.pace,
                                avgHeartRate: matchingActivity.avg_heart_rate || 150,
                                cadence: matchingActivity.cadence || 170,
                                route: {
                                    distance: matchingActivity.distance || localActivityData.route.distance,
                                    type: matchingActivity.activity_type || 'running',
                                    points: matchingActivity.route_points || localActivityData.route.points
                                }
                            };
                        } else {
                            // If no matching activity found, use localStorage data but create activity in DB
                            activityData.value = localActivityData;                                // Create a new activity in the database
                                try {
                                    const createResponse = await axios.post('/api/activities', {
                                        name: localActivityData.name,
                                        route_points: localActivityData.route.points,
                                        activity_date: localActivityData.date,
                                        activity_time: localActivityData.time,
                                        duration: localActivityData.duration,
                                        pace: localActivityData.pace,
                                        activity_type: localActivityData.route.type || 'running',
                                        avg_heart_rate: localActivityData.avgHeartRate,
                                        cadence: localActivityData.cadence,
                                        distance: localActivityData.route.distance
                                    });

                                    // Store the newly created activity ID
                                    activityId.value = createResponse.data.activity.id;
                            } catch (error) {
                                console.error('Error creating activity:', error);
                            }
                        }
                    } else {
                        // If not logged in, just use localStorage data
                        activityData.value = localActivityData;
                    }
                } catch (error) {
                    console.error('Error fetching activities:', error);
                    // Fall back to localStorage data if API fetch fails
                    activityData.value = localActivityData;
                }

                // Set the filename based on activity data
                if (activityData.value) {
                    fileName.value = sanitizeFileName(activityData.value.name) + '.' + fileFormat.value;
                }
            }
        };

        const sanitizeFileName = (name) => {
            return name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
        };

        const filePreview = computed(() => {
            if (!activityData.value) return '';

            if (fileFormat.value === 'gpx') {
                return generateGpxPreview();
            } else if (fileFormat.value === 'tcx') {
                return generateTcxPreview();
            } else {
                return generateCsvPreview();
            }
        });

        const generateGpxPreview = () => {
            if (!activityData.value || !activityData.value.route.points || activityData.value.route.points.length === 0) {
                return 'No route points available';
            }

            const points = activityData.value.route.points;
            const startTime = new Date(activityData.value.date + 'T' + activityData.value.time);
            const totalDuration = activityData.value.duration;

            let gpxHeader = `<?xml version="1.0" encoding="UTF-8"?>
<gpx creator="CreateYourRun" version="1.1" xmlns="http://www.topografix.com/GPX/1/1" xmlns:gpxtpx="http://www.garmin.com/xmlschemas/TrackPointExtension/v1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <metadata>
    <name>${activityData.value.name}</name>
    <time>${startTime.toISOString()}</time>
    <desc>${activityData.value.route.type} activity created with CreateYourRun</desc>
  </metadata>
  <trk>
    <name>${activityData.value.name}</name>
    <type>${activityData.value.route.type}</type>
    <trkseg>`;

            let gpxPoints = '';

            // Generate all trackpoints
            for (let i = 0; i < points.length; i++) {
                const point = points[i];
                // Calculate time for this point based on its position in the route
                const pointTime = new Date(startTime.getTime() + (totalDuration * (i / (points.length - 1)) * 1000));

                // Generate elevation value - low range (0-2m) with one decimal place for Strava compatibility
                const elevation = includeElevation.value ? (Math.floor(Math.random() * 20) / 10).toFixed(1) : 0;

                // Add the trackpoint
                gpxPoints += `
      <trkpt lat="${point[0]}" lon="${point[1]}">
        <ele>${elevation}</ele>
        <time>${pointTime.toISOString()}</time>
        <extensions>
          <gpxtpx:TrackPointExtension>
            ${includeHeartRate.value ? '<gpxtpx:hr>' + activityData.value.avgHeartRate + '</gpxtpx:hr>' : ''}
            ${includeCadence.value ? '<gpxtpx:cad>' + activityData.value.cadence + '</gpxtpx:cad>' : ''}
          </gpxtpx:TrackPointExtension>
        </extensions>
      </trkpt>`;
            }

            let gpxFooter = `
    </trkseg>
  </trk>
</gpx>`;

            return gpxHeader + gpxPoints + gpxFooter;
        };

        const generateTcxPreview = () => {
            if (!activityData.value || !activityData.value.route.points || activityData.value.route.points.length === 0) {
                return 'No route points available';
            }

            const points = activityData.value.route.points;
            const startTime = new Date(activityData.value.date + 'T' + activityData.value.time);
            const totalDuration = activityData.value.duration;
            const totalDistance = parseFloat(activityData.value.route.distance) * 1000; // Convert to meters

            let tcxHeader = `<?xml version="1.0" encoding="UTF-8"?>
<TrainingCenterDatabase xmlns="http://www.garmin.com/xmlschemas/TrainingCenterDatabase/v2">
  <Activities>
    <Activity Sport="${activityData.value.route.type.charAt(0).toUpperCase() + activityData.value.route.type.slice(1)}">
      <Id>${startTime.toISOString()}</Id>
      <Lap StartTime="${startTime.toISOString()}">
        <TotalTimeSeconds>${activityData.value.duration}</TotalTimeSeconds>
        <DistanceMeters>${totalDistance}</DistanceMeters>
        <MaximumSpeed>${12}</MaximumSpeed>
        <Calories>${Math.floor(parseFloat(activityData.value.route.distance) * 60)}</Calories>
        ${includeHeartRate.value ? '<AverageHeartRateBpm><Value>' + activityData.value.avgHeartRate + '</Value></AverageHeartRateBpm>' : ''}
        <Intensity>Active</Intensity>
        <TriggerMethod>Manual</TriggerMethod>
        <Track>`;

            let tcxPoints = '';

            // Generate all trackpoints
            for (let i = 0; i < points.length; i++) {
                const point = points[i];
                // Calculate time for this point based on its position in the route
                const pointTime = new Date(startTime.getTime() + (totalDuration * (i / (points.length - 1)) * 1000));

                // Calculate distance for this point based on its position in the route
                const pointDistance = totalDistance * (i / (points.length - 1));

                // Generate elevation value - low range (0-2m) with one decimal place for Strava compatibility
                const elevation = includeElevation.value ? (Math.floor(Math.random() * 20) / 10).toFixed(1) : 0;

                // Add the trackpoint
                tcxPoints += `
          <Trackpoint>
            <Time>${pointTime.toISOString()}</Time>
            <Position>
              <LatitudeDegrees>${point[0]}</LatitudeDegrees>
              <LongitudeDegrees>${point[1]}</LongitudeDegrees>
            </Position>
            ${includeElevation.value ? '<AltitudeMeters>' + elevation + '</AltitudeMeters>' : ''}
            <DistanceMeters>${pointDistance.toFixed(1)}</DistanceMeters>
            ${includeHeartRate.value ? '<HeartRateBpm><Value>' + activityData.value.avgHeartRate + '</Value></HeartRateBpm>' : ''}
            ${includeCadence.value ? '<Cadence>' + activityData.value.cadence + '</Cadence>' : ''}
            <Extensions>
              <TPX xmlns="http://www.garmin.com/xmlschemas/ActivityExtension/v2">
                <Speed>${(12 * 1000 / 3600).toFixed(2)}</Speed>
                ${activityData.value.route.type === 'running' ? `<RunCadence>${activityData.value.cadence || 170}</RunCadence>` :
                  activityData.value.route.type === 'cycling' ? `<Cadence>${activityData.value.cadence || 85}</Cadence>` :
                  `<Cadence>${activityData.value.cadence || 170}</Cadence>`}
              </TPX>
            </Extensions>
          </Trackpoint>`;
            }

            let tcxFooter = `
        </Track>
      </Lap>
    </Activity>
  </Activities>
</TrainingCenterDatabase>`;

            return tcxHeader + tcxPoints + tcxFooter;
        };

        const generateCsvPreview = () => {
            if (!activityData.value || !activityData.value.route.points || activityData.value.route.points.length === 0) {
                return 'No route points available';
            }

            let header = 'timestamp,latitude,longitude,activity_type';
            if (includeElevation.value) header += ',elevation';
            if (includeHeartRate.value) header += ',heart_rate';
            if (includeCadence.value) header += ',cadence';
            header += ',pace,speed';

            let rows = [];
            rows.push(header);

            const startTime = new Date(activityData.value.date + 'T' + activityData.value.time);
            const totalDuration = activityData.value.duration;
            const points = activityData.value.route.points;

            // Generate all data points
            for (let i = 0; i < points.length; i++) {
                const point = points[i];
                // Calculate time for this point based on its position in the route
                const pointTime = new Date(startTime.getTime() + (totalDuration * (i / (points.length - 1)) * 1000));

                // Generate elevation value - low range (0-2m) with one decimal place for Strava compatibility
                const elevation = includeElevation.value ? (Math.floor(Math.random() * 20) / 10).toFixed(1) : 0;

                // Calculate pace and speed values for running
                // Convert numeric pace (minutes per km) to MM:SS format
                let paceValue = activityData.value.pace || 5.0; // Default pace in min/km
                const paceMinutes = Math.floor(paceValue);
                const paceSeconds = Math.floor((paceValue - paceMinutes) * 60);
                const pace = `${paceMinutes}:${paceSeconds.toString().padStart(2, '0')}`; // Format as MM:SS

                // Calculate speed in km/h (60 divided by pace in minutes)
                const speed = 60 / paceValue;

                // Create the CSV row
                let row = `${pointTime.toISOString()},${point[0]},${point[1]},${activityData.value.route.type}`;
                if (includeElevation.value) row += `,${elevation}`;
                if (includeHeartRate.value) row += `,${activityData.value.avgHeartRate}`;
                if (includeCadence.value) row += `,${activityData.value.cadence}`;
                row += `,${pace},${speed}`;

                rows.push(row);
            }

            return rows.join('\n');
        };

        const downloadFile = async () => {
            if (!activityData.value) return;
            if (!user.value || !hasTokens.value) {
                alert(authError.value || 'You must be logged in with available tokens to export activities');
                return;
            }

            // Ensure we have a valid activity ID from the database
            if (!activityId.value) {
                alert('Activity not found in database. Please try refreshing the page.');
                return;
            }

            try {
                // For all formats, check token usage first
                await useToken();

                // Make sure we're using a properly capitalized activity type for Strava
                // (Strava expects proper capitalization like "Running" rather than "running")
                // This preserves the user's choice of activity type from Activity Generator

                if (fileFormat.value === 'gpx') {
                    await downloadGpxFromServer();
                } else if (fileFormat.value === 'tcx') {
                    // TCX format - ensure we're using proper activity type with first letter capitalized
                    const activityType = activityData.value.route.type;
                    const capitalizedType = activityType.charAt(0).toUpperCase() + activityType.slice(1);
                    const content = generateTcxPreview();
                    const blob = new Blob([content], { type: getMimeType() });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName.value;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    // Log the exported activity
                    await logExportedActivity();
                } else {
                    // For CSV format
                    const content = generateCsvPreview();
                    const blob = new Blob([content], { type: getMimeType() });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName.value;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    // Log the exported activity
                    await logExportedActivity();
                }
            } catch (error) {
                console.error('Export error:', error);
                alert('Error exporting file. Please try again.');
            }
        };

        const useToken = async () => {
            try {
                const response = await axios.post('/api/use-token');

                // Update local user data with new token count
                user.value = response.data.user;
                hasTokens.value = user.value.tokens > 0;

                return true;
            } catch (error) {
                console.error('Token usage error:', error);
                if (error.response && error.response.status === 403) {
                    authError.value = 'Not enough tokens. Please purchase more tokens.';
                    hasTokens.value = false;
                    throw new Error('Not enough tokens');
                }
                throw error;
            }
        };

        const logExportedActivity = async () => {
            try {
                await axios.post('/api/exported-activities', {
                    activity_id: activityId.value, // Use the activity ID from the database
                    format: fileFormat.value
                });
            } catch (error) {
                console.error('Failed to log exported activity:', error);
                // Non-blocking error, continue with export
            }
        };

        const downloadGpxFromServer = async () => {
            try {
                // Prepare request data to send to the backend
                const requestData = {
                    name: activityData.value.name,
                    routePoints: activityData.value.route.points,
                    activityDate: activityData.value.date,
                    activityTime: activityData.value.time,
                    duration: activityData.value.duration,
                    pace: activityData.value.pace,
                    distance: activityData.value.route.distance,
                    includeHeartRate: includeHeartRate.value,
                    includeCadence: includeCadence.value,
                    includeElevation: includeElevation.value,
                    heartRate: activityData.value.avgHeartRate,
                    cadence: activityData.value.cadence
                };

                // If we have an activity ID from the database, include it
                if (activityId.value) {
                    requestData.activity_id = activityId.value;
                }

                // Make API call to the backend to generate GPX using phpGPX
                const response = await axios.post('/api/export/gpx', requestData);

                // Get the GPX content and filename from the response
                const { content, filename } = response.data;

                // Create a Blob with the GPX content returned from the server
                const blob = new Blob([content], { type: 'application/gpx+xml' });
                const url = URL.createObjectURL(blob);

                // Trigger the download
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // Update user tokens if returned in the response
                if (response.data.tokens_remaining !== undefined) {
                    user.value.tokens = response.data.tokens_remaining;
                    hasTokens.value = user.value.tokens > 0;
                }

                // Log the exported activity
                await logExportedActivity();
            } catch (error) {
                console.error('Error downloading GPX:', error);
                if (error.response && error.response.status === 403) {
                    authError.value = 'Not enough tokens. Please purchase more tokens.';
                    hasTokens.value = false;
                } else {
                    alert('Error generating GPX file. Please try again.');
                }
            }
        };

        const getMimeType = () => {
            switch (fileFormat.value) {
                case 'gpx': return 'application/gpx+xml';
                case 'tcx': return 'application/xml';
                case 'csv': return 'text/csv';
                default: return 'text/plain';
            }
        };

        const formatDate = (dateString) => {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        };

        const formatDuration = (seconds) => {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);

            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        };

        const goToActivityGenerator = () => {
            router.push({ name: 'activity-generator' });
        };

        const goToRouteCreator = () => {
            router.push({ name: 'activity-generator' });
        };

        // When fileFormat changes, update the fileName extension
        watch(fileFormat, (newFormat) => {
            if (activityData.value) {
                const baseName = sanitizeFileName(activityData.value.name);
                fileName.value = `${baseName}.${newFormat}`;
            }
        });

        return {
            activityData,
            fileFormat,
            includeHeartRate,
            includeCadence,
            includeElevation,
            fileName,
            filePreview,
            downloadFile,
            formatDate,
            formatDuration,
            goToActivityGenerator,
            goToRouteCreator,
            user,
            hasTokens,
            authError,
            router,
            activityId
        };
    }
}
</script>

<style>
/* Strava style customizations */
input[type="checkbox"] {
    accent-color: #FC4C02;
}
</style>
