<?php

// Test our pace formatting function
function formatPaceForGpx(float $pace): string
{
    // Pace is already in minutes per kilometer, so we just need to split it into minutes and seconds
    $minutes = floor($pace);
    $seconds = floor(($pace - $minutes) * 60);
    
    // Format as MM:SS
    return sprintf('%d:%02d', $minutes, $seconds);
}

// Test with some sample values
$testPaces = [
    11.60, // Your original value
    5.25,
    4.50,
    6.75
];

foreach ($testPaces as $pace) {
    echo "Pace $pace min/km => " . formatPaceForGpx($pace) . " (MM:SS format)\n";
}
